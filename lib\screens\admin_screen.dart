import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gofuelapp/providers/user_provider.dart';
import 'package:gofuelapp/providers/order_provider.dart';
import 'package:gofuelapp/models/order.dart';
// Import to access navigatorKey

class AdminScreen extends StatelessWidget {
  const AdminScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final user = userProvider.user;
    final orderProvider = Provider.of<OrderProvider>(context);
    final allOrders = orderProvider.orders;

    // Calculate fuel type distribution
    Map<String, int> fuelTypeDistribution = {};
    for (var order in allOrders) {
      fuelTypeDistribution[order.fuelType] = (fuelTypeDistribution[order.fuelType] ?? 0) + 1;
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        automaticallyImplyLeading: false, // Remove back button
        actions: [
          // Add logout button
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () async {
              await userProvider.signOut();
              if (context.mounted) {
                Navigator.pushReplacementNamed(context, '/login');
              }
            },
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Admin welcome card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: Colors.yellow.withOpacity(0.2),
                      child: Text(
                        user?.name.substring(0, 1) ?? 'A',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.yellow,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Welcome, Admin',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade400,
                          ),
                        ),
                        Text(
                          user?.name ?? 'Admin User',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.yellow,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Dashboard stats
            const Text(
              'Dashboard',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Orders',
                    allOrders.length.toString(),
                    Icons.receipt_long,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    'Active Orders',
                    allOrders.where((order) => 
                      order.status == OrderStatus.pending || 
                      order.status == OrderStatus.confirmed || 
                      order.status == OrderStatus.dispatched
                    ).length.toString(),
                    Icons.local_shipping,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Recent orders
            const Text(
              'Recent Orders',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Show actual orders or a message if no orders
            allOrders.isEmpty
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    'No orders yet',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade400,
                    ),
                  ),
                ),
              )
            : Column(
                children: allOrders.map((order) => _buildOrderItem(
                  context,
                  order.id.substring(0, 8),
                  order.userId,
                  '${order.fuelType} - ${order.quantity.toInt()}L',
                  order.status.toString().split('.').last,
                  order.orderDate,
                )).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderItem(BuildContext context, String orderId, String userName, String fuelInfo, String status, DateTime orderDate) {
    Color statusColor;
    switch (status.toLowerCase()) {
      case 'pending':
        statusColor = Colors.orange;
        break;
      case 'confirmed':
        statusColor = Colors.blue;
        break;
      case 'dispatched':
        statusColor = Colors.purple;
        break;
      case 'delivered':
        statusColor = Colors.green;
        break;
      case 'cancelled':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }

    // Find the full order object from the provider
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final order = orderProvider.orders.firstWhere(
      (o) => o.id.substring(0, 8) == orderId,
      orElse: () => Order(
        id: '',
        userId: '',
        fuelType: '',
        quantity: 0,
        pricePerLiter: 0,
        totalAmount: 0,
        address: '',
        orderDate: DateTime.now(),
        paymentMethod: 'Cash on Delivery', // Add this line
      ),
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Order #$orderId',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor),
                  ),
                  child: Text(
                    status,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text('User: $userName'),
            Text('Fuel: $fuelInfo'),
            Text('Date: ${orderDate.toLocal().toString().split(' ')[0]}'),
            
            // Add payment method info
            Row(
              children: [
                Icon(
                  order.paymentMethod == 'Card Payment' ? Icons.credit_card : Icons.money,
                  color: Colors.yellow,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  '${order.paymentMethod} ${order.isPaid ? "(Paid)" : "(Unpaid)"}',
                  style: TextStyle(
                    fontSize: 15,
                    color: order.isPaid ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
            
            // Add status change buttons
            if (order.id.isNotEmpty && status.toLowerCase() != 'cancelled')
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // For Cash on Delivery orders that aren't paid yet
                    if (order.paymentMethod == 'Cash on Delivery' && !order.isPaid && status.toLowerCase() == 'confirmed')
                      _buildStatusButton(
                        'Mark as Paid',
                        Colors.green,
                        () => _markOrderAsPaid(context, order.id),
                      ),
                    
                    if (status.toLowerCase() == 'pending')
                      _buildStatusButton(
                        'Confirm',
                        Colors.blue,
                        () => _updateOrderStatus(context, order.id, OrderStatus.confirmed),
                      ),
                    if (status.toLowerCase() == 'confirmed')
                      _buildStatusButton(
                        'Dispatch',
                        Colors.purple,
                        () => _updateOrderStatus(context, order.id, OrderStatus.dispatched),
                      ),
                    if (status.toLowerCase() == 'dispatched')
                      _buildStatusButton(
                        'Deliver',
                        Colors.green,
                        () => _updateOrderStatus(context, order.id, OrderStatus.delivered),
                      ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(String label, Color color, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          textStyle: const TextStyle(fontSize: 12),
          minimumSize: const Size(0, 32),
        ),
        child: Text(label),
      ),
    );
  }

  void _updateOrderStatus(BuildContext context, String orderId, OrderStatus newStatus) {
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    orderProvider.updateOrderStatus(orderId, newStatus);
    
    // Show a snackbar to confirm the status change
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Order status updated to ${newStatus.toString().split('.').last}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _markOrderAsPaid(BuildContext context, String orderId) {
    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final order = orderProvider.orders.firstWhere((o) => o.id == orderId);
      
      // Update the order's isPaid status
      order.isPaid = true;
      orderProvider.notifyListeners(); // Force UI update
      
      // Show a snackbar to confirm
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Order marked as paid'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 2),
        ),
      );
    } catch (e) {
      // Handle any errors
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error updating payment status: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}












