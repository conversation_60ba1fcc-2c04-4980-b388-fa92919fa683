# Google Maps API Setup Guide for GoFuel App

This guide will help you configure Google Maps API for your GoFuel Flutter application.

## Prerequisites

1. A Google Cloud Platform (GCP) account
2. A project in Google Cloud Console
3. Billing enabled on your GCP project (required for Maps API)

## Step 1: Create and Configure Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable billing for your project (required for Maps APIs)

## Step 2: Enable Required APIs

Navigate to the [Google Cloud Console APIs & Services](https://console.cloud.google.com/apis/library) and enable the following APIs:

1. **Maps JavaScript API** (for web)
2. **Maps SDK for Android** (for Android)
3. **Maps SDK for iOS** (for iOS)
4. **Places API** (for location search)
5. **Geocoding API** (for address conversion)

## Step 3: Create API Keys

1. Go to [Credentials](https://console.cloud.google.com/apis/credentials)
2. Click "Create Credentials" → "API Key"
3. Copy the generated API key

### Secure Your API Key (Recommended)

1. Click on your API key to edit it
2. Under "Application restrictions", choose the appropriate option:
   - **HTTP referrers** for web
   - **Android apps** for Android (add your package name and SHA-1 fingerprint)
   - **iOS apps** for iOS (add your bundle identifier)

## Step 4: Configure Your Flutter App

### For Web (web/index.html)

Replace `YOUR_GOOGLE_MAPS_API_KEY` in `web/index.html` with your actual API key:

```html
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_ACTUAL_API_KEY&libraries=places"></script>
```

### For Android (android/app/src/main/AndroidManifest.xml)

Replace `YOUR_GOOGLE_MAPS_API_KEY` in the Android manifest with your actual API key:

```xml
<meta-data
    android:name="com.google.android.geo.API_KEY"
    android:value="YOUR_ACTUAL_API_KEY" />
```

### For iOS (ios/Runner/AppDelegate.swift)

Replace `YOUR_GOOGLE_MAPS_API_KEY` in the iOS AppDelegate with your actual API key:

```swift
GMSServices.provideAPIKey("YOUR_ACTUAL_API_KEY")
```

## Step 5: Test Your Configuration

1. Run your Flutter app
2. Navigate to the "Order Fuel" screen
3. Click "Select Location on Map"
4. The map should load successfully

## Troubleshooting

### Common Issues:

1. **"Oops! Something went wrong" error**
   - Check if your API key is correctly configured
   - Ensure billing is enabled on your GCP project
   - Verify that all required APIs are enabled

2. **Map shows gray area**
   - API key might be invalid or restricted
   - Check browser console for specific error messages

3. **"This page can't load Google Maps correctly"**
   - API key is missing or incorrect
   - Required APIs are not enabled
   - Billing is not enabled

### Getting Your Android SHA-1 Fingerprint:

For debug builds:
```bash
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

For release builds:
```bash
keytool -list -v -keystore /path/to/your/keystore.jks -alias your_alias_name
```

## Security Best Practices

1. **Never commit API keys to version control**
2. **Use environment variables or secure storage for API keys**
3. **Restrict API keys to specific platforms and APIs**
4. **Monitor API usage in Google Cloud Console**
5. **Set up usage quotas and alerts**

## Cost Management

- Google Maps API has a free tier with monthly credits
- Monitor your usage in Google Cloud Console
- Set up billing alerts to avoid unexpected charges
- Consider implementing caching for frequently accessed locations

## Support

If you encounter issues:
1. Check the [Google Maps Platform documentation](https://developers.google.com/maps/documentation)
2. Review the [Flutter Google Maps plugin documentation](https://pub.dev/packages/google_maps_flutter)
3. Check the browser console for specific error messages
