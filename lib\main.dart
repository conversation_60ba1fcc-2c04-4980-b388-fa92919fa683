import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gofuelapp/providers/user_provider.dart';
import 'package:gofuelapp/providers/order_provider.dart';
import 'package:gofuelapp/screens/splash_screen.dart';
import 'package:gofuelapp/screens/login_screen.dart';
import 'package:gofuelapp/screens/register_screen.dart';
import 'package:gofuelapp/screens/home_screen.dart';
import 'package:gofuelapp/screens/admin_screen.dart';
import 'package:gofuelapp/services/firebase_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Add a global navigator key
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  try {
    await FirebaseService.initializeFirebase();
  } catch (e) {
    // Firebase initialization failed, but app can still run
  }
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProxyProvider<UserProvider, OrderProvider>(
          create: (_) => OrderProvider(),
          update: (_, userProvider, orderProvider) {
            userProvider.onUserChanged = (String? userId) {
              if (userId != null) {
                final user = userProvider.user;
                if (user?.isAdmin == true) {
                  orderProvider?.loadAllOrders();
                } else {
                  orderProvider?.loadUserOrders(userId);
                }
              } else {
                orderProvider?.clearOrders();
              }
            };
            return orderProvider!;
          },
        ),
        Provider<FirebaseFirestore>.value(value: FirebaseFirestore.instance),
      ],
      child: MaterialApp(
        title: 'GoFuel App',
        debugShowCheckedModeBanner: false,
        navigatorKey: navigatorKey,
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.dark(
            primary: Colors.yellow,
            secondary: Colors.yellow.shade700,
            surface: const Color(0xFF1E1E1E),
            onPrimary: Colors.black,
            onSecondary: Colors.black,
            onSurface: Colors.white,
            brightness: Brightness.dark,
          ),
          scaffoldBackgroundColor: const Color(0xFF121212),
          appBarTheme: const AppBarTheme(
            centerTitle: true,
            elevation: 0,
            backgroundColor: Color(0xFF1E1E1E),
            foregroundColor: Colors.yellow,
          ),
          cardTheme: CardThemeData(
            color: const Color(0xFF1E1E1E),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            surfaceTintColor: Colors.transparent,
            shadowColor: Colors.black,
          ),
          elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.yellow,
              foregroundColor: Colors.black,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          outlinedButtonTheme: OutlinedButtonThemeData(
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.yellow,
              side: const BorderSide(color: Colors.yellow),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: Colors.yellow,
            ),
          ),
          inputDecorationTheme: InputDecorationTheme(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.yellow),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.yellow, width: 2),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.yellow.withValues(alpha: 0.5)),
            ),
            labelStyle: TextStyle(color: Colors.yellow.withValues(alpha: 0.8)),
            prefixIconColor: Colors.yellow.withValues(alpha: 0.8),
            suffixIconColor: Colors.yellow.withValues(alpha: 0.8),
          ),
          iconTheme: const IconThemeData(
            color: Colors.yellow,
          ),
          dividerTheme: DividerThemeData(
            color: Colors.yellow.withValues(alpha: 0.2),
          ),
          progressIndicatorTheme: const ProgressIndicatorThemeData(
            color: Colors.yellow,
          ),
        ),
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashScreen(),
          '/login': (context) => const LoginScreen(),
          '/register': (context) => const RegisterScreen(),
          '/home': (context) => const HomeScreen(),
          '/admin': (context) => const AdminScreen(),
        },
      ),
    );
  }
}
