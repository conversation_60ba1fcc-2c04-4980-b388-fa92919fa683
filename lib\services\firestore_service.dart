import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:gofuelapp/models/user.dart';
import 'package:gofuelapp/models/order.dart' as app_order;
import 'package:gofuelapp/models/station.dart';

class FirestoreService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // User methods
  Future<void> createUser(User user) async {
    await _firestore.collection('users').doc(user.id).set({
      'name': user.name,
      'email': user.email,
      'phone': user.phone ?? '',
      'profileImage': user.profileImage ?? '',
      'isAdmin': user.isAdmin,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  Future<User?> getUser(String userId) async {
    final doc = await _firestore.collection('users').doc(userId).get();
    if (!doc.exists) return null;
    
    final data = doc.data() as Map<String, dynamic>;
    return User(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'],
      profileImage: data['profileImage'],
      isAdmin: data['isAdmin'] ?? false,
    );
  }

  // Order methods
  Future<String> createOrder(app_order.Order order) async {
    final docRef = await _firestore.collection('orders').add({
      'userId': order.userId,
      'fuelType': order.fuelType,
      'quantity': order.quantity,
      'pricePerLiter': order.pricePerLiter,
      'totalAmount': order.totalAmount,
      'address': order.address,
      'orderDate': Timestamp.fromDate(order.orderDate),
      'paymentMethod': order.paymentMethod,
      'status': order.status.toString().split('.').last,
      'isPaid': order.isPaid,
      'createdAt': FieldValue.serverTimestamp(),
    });
    
    return docRef.id;
  }

  Future<void> updateOrderStatus(String orderId, app_order.OrderStatus status) async {
    await _firestore.collection('orders').doc(orderId).update({
      'status': status.toString().split('.').last,
    });
  }

  Future<void> markOrderAsPaid(String orderId) async {
    await _firestore.collection('orders').doc(orderId).update({
      'isPaid': true,
    });
  }

  Stream<List<app_order.Order>> getUserOrders(String userId) {
    // Temporary fix: Remove orderBy to avoid index requirement
    return _firestore.collection('orders')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) {
          final orders = snapshot.docs.map((doc) {
            final data = doc.data();
            return app_order.Order(
              id: doc.id,
              userId: data['userId'],
              fuelType: data['fuelType'],
              quantity: data['quantity'],
              pricePerLiter: data['pricePerLiter'],
              totalAmount: data['totalAmount'],
              address: data['address'],
              orderDate: (data['orderDate'] as Timestamp).toDate(),
              paymentMethod: data['paymentMethod'],
              status: _parseOrderStatus(data['status']),
              isPaid: data['isPaid'] ?? false,
            );
          }).toList();

          // Sort in memory instead of in Firestore query
          orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));
          return orders;
        });
  }

  Stream<List<app_order.Order>> getAllOrders() {
    // Temporary fix: Remove orderBy to avoid index requirement
    return _firestore.collection('orders')
        .snapshots()
        .map((snapshot) {
          final orders = snapshot.docs.map((doc) {
            final data = doc.data();
            return app_order.Order(
              id: doc.id,
              userId: data['userId'],
              fuelType: data['fuelType'],
              quantity: data['quantity'],
              pricePerLiter: data['pricePerLiter'],
              totalAmount: data['totalAmount'],
              address: data['address'],
              orderDate: (data['orderDate'] as Timestamp).toDate(),
              paymentMethod: data['paymentMethod'],
              status: _parseOrderStatus(data['status']),
              isPaid: data['isPaid'] ?? false,
            );
          }).toList();

          // Sort in memory instead of in Firestore query
          orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));
          return orders;
        });
  }

  // Station methods
  Future<void> createStation(FuelStation station) async {
    await _firestore.collection('stations').doc(station.id).set({
      'name': station.name,
      'address': station.address,
      'distance': station.distance,
      'rating': station.rating,
      'deliveryCharge': station.deliveryCharge,
      'fuelTypes': station.fuelTypes,
      'prices': station.prices,
    });
  }

  Future<List<FuelStation>> getAllStations() async {
    final snapshot = await _firestore.collection('stations').get();
    return snapshot.docs.map((doc) {
      final data = doc.data();
      return FuelStation(
        id: doc.id,
        name: data['name'],
        address: data['address'],
        distance: data['distance'],
        rating: data['rating'],
        deliveryCharge: data['deliveryCharge'],
        fuelTypes: List<String>.from(data['fuelTypes']),
        prices: Map<String, double>.from(data['prices']),
      );
    }).toList();
  }

  // Fuel prices methods
  Future<void> updateFuelPrices(Map<String, double> prices) async {
    await _firestore.collection('fuel_prices').doc('current').set({
      'prices': prices,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  Future<Map<String, double>> getCurrentFuelPrices() async {
    try {
      final doc = await _firestore.collection('fuel_prices').doc('current').get();
      if (!doc.exists) {
        return {
          'Petrol': 253.63,
          'Diesel': 254.64,
          'High Octane': 275.65,
        };
      }
      
      final data = doc.data() as Map<String, dynamic>;
      return Map<String, double>.from(data['prices']);
    } catch (e) {
      print('Error getting fuel prices: $e');
      return {
        'Petrol': 253.63,
        'Diesel': 254.64,
        'High Octane': 275.65,
      };
    }
  }

  // Helper methods
  app_order.OrderStatus _parseOrderStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return app_order.OrderStatus.pending;
      case 'confirmed':
        return app_order.OrderStatus.confirmed;
      case 'dispatched':
        return app_order.OrderStatus.dispatched;
      case 'delivered':
        return app_order.OrderStatus.delivered;
      case 'cancelled':
        return app_order.OrderStatus.cancelled;
      default:
        return app_order.OrderStatus.pending;
    }
  }
}
