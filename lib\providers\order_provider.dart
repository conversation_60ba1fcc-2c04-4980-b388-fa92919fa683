import 'package:flutter/foundation.dart';
import 'package:gofuelapp/models/order.dart';
import 'package:gofuelapp/services/firestore_service.dart';
import 'dart:async';

class OrderProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<List<Order>>? _ordersSubscription;
  String? _currentUserId;

  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get orders by user ID
  List<Order> getOrdersByUserId(String userId) {
    return _orders.where((order) => order.userId == userId).toList();
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    super.dispose();
  }

  // Load user orders
  Future<void> loadUserOrders(String userId) async {
    print('OrderProvider: Loading orders for user: $userId'); // Debug log

    // Cancel previous subscription if exists
    _ordersSubscription?.cancel();

    _currentUserId = userId;
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _ordersSubscription = _firestoreService.getUserOrders(userId).listen((orders) {
        print('OrderProvider: Received ${orders.length} orders from Firestore'); // Debug log
        _orders = orders;
        _isLoading = false;
        notifyListeners();
      }, onError: (e) {
        print('OrderProvider: Error loading orders: $e'); // Debug log
        _error = e.toString();
        _isLoading = false;
        notifyListeners();
      });
    } catch (e) {
      print('OrderProvider: Exception loading orders: $e'); // Debug log
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear orders when user logs out
  void clearOrders() {
    _ordersSubscription?.cancel();
    _orders.clear();
    _currentUserId = null;
    _error = null;
    notifyListeners();
  }

  // Load all orders (for admin)
  Future<void> loadAllOrders() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _firestoreService.getAllOrders().listen((orders) {
        _orders = orders;
        _isLoading = false;
        notifyListeners();
      }, onError: (e) {
        _error = e.toString();
        _isLoading = false;
        notifyListeners();
      });
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add a new order
  Future<void> addOrder(Order order) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final orderId = await _firestoreService.createOrder(order);
      // Update the order with the new ID from Firestore
      final newOrder = Order(
        id: orderId,
        userId: order.userId,
        fuelType: order.fuelType,
        quantity: order.quantity,
        pricePerLiter: order.pricePerLiter,
        totalAmount: order.totalAmount,
        address: order.address,
        orderDate: order.orderDate,
        paymentMethod: order.paymentMethod,
        status: order.status,
        isPaid: order.isPaid,
      );
      
      _orders.add(newOrder);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update order status
  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestoreService.updateOrderStatus(orderId, newStatus);
      
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex].status = newStatus;
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Mark order as paid
  Future<void> markOrderAsPaid(String orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _firestoreService.markOrderAsPaid(orderId);
      
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex != -1) {
        _orders[orderIndex].isPaid = true;
      }
      
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
}








