import 'package:flutter/foundation.dart';
import 'package:gofuelapp/models/order.dart';
import 'package:gofuelapp/services/firestore_service.dart';
import 'dart:async';

class OrderProvider with ChangeNotifier {
  final FirestoreService _firestoreService = FirestoreService();
  List<Order> _orders = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<List<Order>>? _ordersSubscription;


  List<Order> get orders => _orders;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get orders by user ID
  List<Order> getOrdersByUserId(String userId) {
    return _orders.where((order) => order.userId == userId).toList();
  }

  @override
  void dispose() {
    _ordersSubscription?.cancel();
    super.dispose();
  }

  Future<void> loadUserOrders(String userId) async {
    _ordersSubscription?.cancel();
    _setLoading(true);

    try {
      _ordersSubscription = _firestoreService.getUserOrders(userId).listen(
        (orders) => _setOrders(orders),
        onError: (e) => _setError(e.toString()),
      );
    } catch (e) {
      _setError(e.toString());
    }
  }

  void clearOrders() {
    _ordersSubscription?.cancel();
    _orders.clear();
    _error = null;
    notifyListeners();
  }

  Future<void> loadAllOrders() async {
    _setLoading(true);
    try {
      _firestoreService.getAllOrders().listen(
        (orders) => _setOrders(orders),
        onError: (e) => _setError(e.toString()),
      );
    } catch (e) {
      _setError(e.toString());
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _error = null;
    notifyListeners();
  }

  void _setOrders(List<Order> orders) {
    _orders = orders;
    _isLoading = false;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  Future<void> addOrder(Order order) async {
    _setLoading(true);
    try {
      final orderId = await _firestoreService.createOrder(order);
      final newOrder = Order(
        id: orderId,
        userId: order.userId,
        fuelType: order.fuelType,
        quantity: order.quantity,
        pricePerLiter: order.pricePerLiter,
        totalAmount: order.totalAmount,
        address: order.address,
        orderDate: order.orderDate,
        paymentMethod: order.paymentMethod,
        status: order.status,
        isPaid: order.isPaid,
      );
      _orders.add(newOrder);
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    _setLoading(true);
    try {
      await _firestoreService.updateOrderStatus(orderId, newStatus);
      _updateLocalOrder(orderId, (order) => order.status = newStatus);
    } catch (e) {
      _setError(e.toString());
    }
  }

  Future<void> markOrderAsPaid(String orderId) async {
    _setLoading(true);
    try {
      await _firestoreService.markOrderAsPaid(orderId);
      _updateLocalOrder(orderId, (order) => order.isPaid = true);
    } catch (e) {
      _setError(e.toString());
    }
  }

  void _updateLocalOrder(String orderId, void Function(Order) updateFunction) {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      updateFunction(_orders[orderIndex]);
    }
    _isLoading = false;
    notifyListeners();
  }
}








