enum OrderStatus {
  pending,
  confirmed,
  dispatched,
  delivered,
  cancelled,
}

class Order {
  final String id;
  final String userId;
  final String fuelType;
  final double quantity;
  final double pricePerLiter;
  final double totalAmount;
  final String address;
  final DateTime orderDate;
  final String paymentMethod;
  OrderStatus status;
  bool isPaid;

  Order({
    required this.id,
    required this.userId,
    required this.fuelType,
    required this.quantity,
    required this.pricePerLiter,
    required this.totalAmount,
    required this.address,
    required this.orderDate,
    this.paymentMethod = 'Cash on Delivery',
    this.status = OrderStatus.pending,
    this.isPaid = false,
  });
}





