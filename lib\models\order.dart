enum OrderStatus { pending, confirmed, dispatched, delivered, cancelled }

class Order {
  final String id, userId, fuelType, address, paymentMethod;
  final double quantity, pricePerLiter, totalAmount;
  final DateTime orderDate;
  OrderStatus status;
  bool isPaid;

  Order({
    required this.id,
    required this.userId,
    required this.fuelType,
    required this.quantity,
    required this.pricePerLiter,
    required this.totalAmount,
    required this.address,
    required this.orderDate,
    this.paymentMethod = 'Cash on Delivery',
    this.status = OrderStatus.pending,
    this.isPaid = false,
  });
}





