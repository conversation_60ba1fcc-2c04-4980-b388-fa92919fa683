import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:gofuelapp/models/user.dart';
import 'package:gofuelapp/services/firebase_service.dart';
import 'package:gofuelapp/services/firestore_service.dart';

class UserProvider with ChangeNotifier {
  final FirebaseAuthService _authService = FirebaseAuthService();
  User? _user;
  bool _isLoading = false;
  String? _error;

  // Callback to notify when user changes (for loading orders)
  Function(String?)? onUserChanged;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  UserProvider() {
    // Listen to auth state changes
    _authService.authStateChanges.listen((firebase_auth.User? firebaseUser) {
      if (firebaseUser == null) {
        _user = null;
        onUserChanged?.call(null); // Notify that user logged out
        notifyListeners();
      } else {
        // Load user data when Firebase user changes
        _loadCurrentUser();
      }
    });
  }

  // Load current authenticated user
  Future<void> _loadCurrentUser() async {
    try {
      final user = await _authService.getCurrentAppUser();
      _user = user;
      print('UserProvider: User loaded: ${user?.id}'); // Debug log
      onUserChanged?.call(user?.id); // Notify that user logged in
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  // Sign up with email and password
  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        name: name,
        phone: phone,
      );

      if (user != null) {
        _user = user;
        onUserChanged?.call(user.id); // Notify that user signed up
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to create account';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final user = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        _user = user;
        onUserChanged?.call(user.id); // Notify that user signed in
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Failed to sign in';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _authService.signOut();
      _user = null;
      onUserChanged?.call(null); // Notify that user signed out
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Reset password
  Future<bool> resetPassword(String email) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _authService.resetPassword(email);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Check if user is currently authenticated
  Future<void> checkAuthState() async {
    _isLoading = true;
    notifyListeners();

    try {
      final user = await _authService.getCurrentAppUser();
      _user = user;
      onUserChanged?.call(user?.id); // Notify that user state checked
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Update user profile
  Future<bool> updateUserProfile({String? name, String? phone}) async {
    if (_user == null) return false;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Update local user object
      if (name != null) _user!.name = name;
      if (phone != null) _user!.phone = phone;

      // Update in Firestore
      final firestoreService = FirestoreService();
      await firestoreService.createUser(_user!);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Legacy methods for backward compatibility
  @Deprecated('Use signIn instead')
  Future<void> setUser(User user) async {
    _user = user;
    notifyListeners();
  }

  @Deprecated('Use checkAuthState instead')
  Future<void> loadUser(String userId) async {
    await checkAuthState();
  }

  @Deprecated('Use signOut instead')
  void clearUser() {
    signOut();
  }

  @Deprecated('Use signOut instead')
  void logout() {
    signOut();
  }
}

