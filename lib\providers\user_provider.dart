import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:gofuelapp/models/user.dart';
import 'package:gofuelapp/services/firebase_service.dart';
import 'package:gofuelapp/services/firestore_service.dart';

class UserProvider with ChangeNotifier {
  final FirebaseAuthService _authService = FirebaseAuthService();
  User? _user;
  bool _isLoading = false;
  String? _error;

  // Simple callback for order loading
  Function(String?)? onUserChanged;

  User? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _user != null;

  UserProvider() {
    _authService.authStateChanges.listen((firebase_auth.User? firebaseUser) {
      if (firebaseUser == null) {
        _user = null;
        onUserChanged?.call(null);
        notifyListeners();
      } else {
        _loadCurrentUser();
      }
    });
  }

  Future<void> _loadCurrentUser() async {
    try {
      _user = await _authService.getCurrentAppUser();
      onUserChanged?.call(_user?.id);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    return _handleAuth(() => _authService.signUpWithEmailAndPassword(
      email: email, password: password, name: name, phone: phone));
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    return _handleAuth(() => _authService.signInWithEmailAndPassword(
      email: email, password: password));
  }

  Future<bool> _handleAuth(Future<User?> Function() authFunction) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _user = await authFunction();
      if (_user != null) {
        onUserChanged?.call(_user!.id);
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _error = 'Authentication failed';
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> signOut() async {
    try {
      await _authService.signOut();
      _user = null;
      onUserChanged?.call(null);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      await _authService.resetPassword(email);
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  Future<void> checkAuthState() async {
    try {
      _user = await _authService.getCurrentAppUser();
      onUserChanged?.call(_user?.id);
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      notifyListeners();
    }
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  Future<bool> updateUserProfile({String? name, String? phone}) async {
    if (_user == null) return false;

    try {
      if (name != null) _user!.name = name;
      if (phone != null) _user!.phone = phone;

      await FirestoreService().createUser(_user!);
      notifyListeners();
      return true;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }
}

