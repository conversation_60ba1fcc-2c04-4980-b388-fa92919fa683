import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class FreeMapLocationPicker extends StatefulWidget {
  final Function(String address, LatLng coordinates) onLocationSelected;
  final LatLng? initialLocation;

  const FreeMapLocationPicker({
    super.key,
    required this.onLocationSelected,
    this.initialLocation,
  });

  @override
  State<FreeMapLocationPicker> createState() => _FreeMapLocationPickerState();
}

class _FreeMapLocationPickerState extends State<FreeMapLocationPicker> {
  final MapController _mapController = MapController();
  final TextEditingController _addressController = TextEditingController();
  LatLng _selectedLocation = const LatLng(24.8607, 67.0011); // Default to Karachi
  List<Marker> _markers = [];
  bool _isLoadingAddress = false;

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _selectedLocation = widget.initialLocation!;
    } else {
      // Default to Karachi
      _selectedLocation = const LatLng(24.8607, 67.0011);
    }
    _updateMarker(_selectedLocation);
    _updateAddress(_selectedLocation);
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  void _updateMarker(LatLng position) {
    setState(() {
      _markers = [
        Marker(
          point: position,
          width: 40,
          height: 40,
          child: const Icon(
            Icons.location_pin,
            color: Colors.red,
            size: 40,
          ),
        ),
      ];
    });
  }

  void _onLocationChanged(LatLng position) {
    setState(() {
      _selectedLocation = position;
    });
    _updateMarker(position);
    _updateAddress(position);
  }

  Future<void> _updateAddress(LatLng coordinates) async {
    setState(() {
      _isLoadingAddress = true;
    });

    try {
      // Use OpenStreetMap's free Nominatim API for reverse geocoding
      final url = 'https://nominatim.openstreetmap.org/reverse?format=json&lat=${coordinates.latitude}&lon=${coordinates.longitude}&zoom=18&addressdetails=1';

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'GoFuelApp/1.0 (Flutter App)', // Required by Nominatim
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['display_name'] != null) {
          final address = data['display_name'] as String;
          setState(() {
            _addressController.text = address;
            _isLoadingAddress = false;
          });
        } else {
          // Fallback to coordinates if no address found
          _setCoordinatesFallback(coordinates);
        }
      } else {
        // Fallback to coordinates if API fails
        _setCoordinatesFallback(coordinates);
      }
    } catch (e) {
      // Fallback to coordinates if any error occurs
      _setCoordinatesFallback(coordinates);
      debugPrint('Error getting address: $e');
    }
  }

  void _setCoordinatesFallback(LatLng coordinates) {
    final address = 'Lat: ${coordinates.latitude.toStringAsFixed(4)}, Lng: ${coordinates.longitude.toStringAsFixed(4)}';
    setState(() {
      _addressController.text = address;
      _isLoadingAddress = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        actions: [
          TextButton(
            onPressed: _addressController.text.isNotEmpty
                ? () {
                    widget.onLocationSelected(_addressController.text, _selectedLocation);
                    Navigator.pop(context);
                  }
                : null,
            child: const Text(
              'CONFIRM',
              style: TextStyle(
                color: Colors.yellow,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Address input
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            color: const Color(0xFF1E1E1E),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Address:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.yellow,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    hintText: 'Enter address or tap on map',
                    border: const OutlineInputBorder(),
                    isDense: true,
                    suffixIcon: _isLoadingAddress
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: Padding(
                            padding: EdgeInsets.all(12.0),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.yellow,
                            ),
                          ),
                        )
                      : null,
                  ),
                  onChanged: (value) {
                    // Address is automatically updated in the text field
                  },
                ),
                const SizedBox(height: 8),
                Text(
                  _isLoadingAddress
                    ? 'Getting address...'
                    : 'Tap on the map to get the address for that location',
                  style: TextStyle(
                    color: _isLoadingAddress ? Colors.yellow : Colors.grey.shade400,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          // Map
          Expanded(
            child: Column(
              children: [
                // No quick location buttons anymore
                const SizedBox(height: 8),
                // Free OpenStreetMap
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          initialCenter: _selectedLocation,
                          initialZoom: 13.0,
                          onTap: (tapPosition, point) {
                            _onLocationChanged(point);
                          },
                        ),
                        children: [
                          TileLayer(
                            urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                            userAgentPackageName: 'com.example.gofuelapp',
                          ),
                          MarkerLayer(
                            markers: _markers,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

        ],
      ),
    );
  }

}
