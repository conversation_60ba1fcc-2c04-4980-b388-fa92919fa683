import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:gofuelapp/firebase_options.dart';
import 'package:gofuelapp/models/user.dart' as app_user;
import 'package:gofuelapp/services/firestore_service.dart';

class FirebaseService {
  static Future<void> initializeFirebase() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
  }
}

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestoreService = FirestoreService();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign up with email and password
  Future<app_user.User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    try {
      final UserCredential result = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? firebaseUser = result.user;
      if (firebaseUser != null) {
        // Update display name
        await firebaseUser.updateDisplayName(name);

        // Create user in Firestore
        final appUser = app_user.User(
          id: firebaseUser.uid,
          name: name,
          email: email,
          phone: phone,
          isAdmin: email.toLowerCase() == '<EMAIL>',
        );

        await _firestoreService.createUser(appUser);
        return appUser;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to create account: ${e.toString()}');
    }
  }

  // Sign in with email and password
  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final User? firebaseUser = result.user;
      if (firebaseUser != null) {
        // Get user data from Firestore
        final appUser = await _firestoreService.getUser(firebaseUser.uid);
        if (appUser != null) {
          return appUser;
        } else {
          // If user doesn't exist in Firestore, create them
          final newUser = app_user.User(
            id: firebaseUser.uid,
            name: firebaseUser.displayName ?? 'User',
            email: firebaseUser.email ?? email,
            isAdmin: email.toLowerCase() == '<EMAIL>',
          );
          await _firestoreService.createUser(newUser);
          return newUser;
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to sign in: ${e.toString()}');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  // Get current app user
  Future<app_user.User?> getCurrentAppUser() async {
    final User? firebaseUser = _auth.currentUser;
    if (firebaseUser != null) {
      return await _firestoreService.getUser(firebaseUser.uid);
    }
    return null;
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }
}
