import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:gofuelapp/firebase_options.dart';
import 'package:gofuelapp/models/user.dart' as app_user;
import 'package:gofuelapp/services/firestore_service.dart';

class FirebaseService {
  static Future<void> initializeFirebase() async {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  }
}

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirestoreService _firestore = FirestoreService();

  User? get currentUser => _auth.currentUser;
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  Future<app_user.User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    try {
      final result = await _auth.createUserWithEmailAndPassword(
        email: email, password: password);
      final firebaseUser = result.user;

      if (firebaseUser != null) {
        await firebaseUser.updateDisplayName(name);
        final appUser = app_user.User(
          id: firebaseUser.uid,
          name: name,
          email: email,
          phone: phone,
          isAdmin: email.toLowerCase() == '<EMAIL>',
        );
        await _firestore.createUser(appUser);
        return appUser;
      }
      return null;
    } catch (e) {
      throw Exception('Failed to create account: ${e.toString()}');
    }
  }

  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final result = await _auth.signInWithEmailAndPassword(
        email: email, password: password);
      final firebaseUser = result.user;

      if (firebaseUser != null) {
        final appUser = await _firestore.getUser(firebaseUser.uid);
        if (appUser != null) {
          return appUser;
        } else {
          final newUser = app_user.User(
            id: firebaseUser.uid,
            name: firebaseUser.displayName ?? 'User',
            email: firebaseUser.email ?? email,
            isAdmin: email.toLowerCase() == '<EMAIL>',
          );
          await _firestore.createUser(newUser);
          return newUser;
        }
      }
      return null;
    } catch (e) {
      throw Exception('Failed to sign in: ${e.toString()}');
    }
  }

  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  Future<app_user.User?> getCurrentAppUser() async {
    final firebaseUser = _auth.currentUser;
    return firebaseUser != null ? await _firestore.getUser(firebaseUser.uid) : null;
  }

  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }
}
