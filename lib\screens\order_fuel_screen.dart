import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:latlong2/latlong.dart';
import 'package:gofuelapp/providers/user_provider.dart';
import 'package:gofuelapp/providers/order_provider.dart';
import 'package:gofuelapp/models/order.dart';
import 'package:gofuelapp/widgets/free_map_location_picker.dart';

class OrderFuelScreen extends StatefulWidget {
  const OrderFuelScreen({super.key});

  @override
  State<OrderFuelScreen> createState() => _OrderFuelScreenState();
}

class _OrderFuelScreenState extends State<OrderFuelScreen> {
  final _formKey = GlobalKey<FormState>();
  String _selectedFuelType = 'Petrol';
  final _quantityController = TextEditingController();
  final _addressController = TextEditingController();
  String _paymentMethod = 'Cash on Delivery'; // Default payment method
  bool _isLoading = false;
  LatLng? _selectedCoordinates;

  final Map<String, double> _fuelPrices = {
    'Petrol': 253.63,
    'Diesel': 254.64,
    'High Octane': 275.65,
  };

  final double _deliveryCharge = 300.0;

  double get _totalAmount {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    return quantity * _fuelPrices[_selectedFuelType]! + _deliveryCharge;
  }

  double get _subtotal{
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    return quantity * _fuelPrices[_selectedFuelType]!;
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _addressController.dispose();
    super.dispose();
  }

  void _openMapLocationPicker() async {
    final result = await Navigator.push<Map<String, dynamic>>(
      context,
      MaterialPageRoute(
        builder: (context) => FreeMapLocationPicker(
          initialLocation: _selectedCoordinates,
          onLocationSelected: (String address, LatLng coordinates) {
            setState(() {
              _addressController.text = address;
              _selectedCoordinates = coordinates;
            });
          },
        ),
      ),
    );
  }

  void _placeOrder() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final userProvider = Provider.of<UserProvider>(context, listen: false);
        final orderProvider = Provider.of<OrderProvider>(context, listen: false);

        if (userProvider.user != null) {
          // If card payment, process it first
          if (_paymentMethod == 'Card Payment') {
            try {
              await _processCardPayment();
            } catch (e) {
              // Payment failed or was cancelled
              setState(() {
                _isLoading = false;
              });
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Payment cancelled or failed'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
              return;
            }
          }

          // Create and add the order
          final newOrder = Order(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            userId: userProvider.user!.id,
            fuelType: _selectedFuelType,
            quantity: double.parse(_quantityController.text),
            pricePerLiter: _fuelPrices[_selectedFuelType]!,
            totalAmount: _totalAmount,
            address: _addressController.text,
            orderDate: DateTime.now(),
            paymentMethod: _paymentMethod,
            isPaid: _paymentMethod == 'Card Payment',
            status: _paymentMethod == 'Card Payment' ? OrderStatus.confirmed : OrderStatus.pending,
          );

          orderProvider.addOrder(newOrder);

          setState(() {
            _isLoading = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(_paymentMethod == 'Card Payment' 
                    ? 'Payment successful! Order confirmed.'
                    : 'Order placed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          }
        }
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _processCardPayment() async {
    final cardNumberController = TextEditingController();
    final cardHolderController = TextEditingController();
    final expiryController = TextEditingController();
    final cvvController = TextEditingController();
    bool isProcessing = false;
    
    // Show card payment form dialog
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Card Payment'),
          content: isProcessing 
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(height: 16),
                  Text('Processing payment of PKR-${_totalAmount.toStringAsFixed(2)}'),
                ],
              )
            : SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Total Amount: PKR-${_totalAmount.toStringAsFixed(2)}', 
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 16),
                    TextField(
                      controller: cardHolderController,
                      decoration: const InputDecoration(
                        labelText: 'Card Holder Name',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextField(
                      controller: cardNumberController,
                      decoration: const InputDecoration(
                        labelText: 'Card Number',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      maxLength: 16,
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: expiryController,
                            decoration: const InputDecoration(
                              labelText: 'Expiry (MM/YY)',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.datetime,
                            maxLength: 5,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: TextField(
                            controller: cvvController,
                            decoration: const InputDecoration(
                              labelText: 'CVV',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            maxLength: 3,
                            obscureText: true,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            if (!isProcessing)
              ElevatedButton(
                onPressed: () async {
                  // Validate card details
                  if (cardHolderController.text.isEmpty ||
                      cardNumberController.text.length < 16 ||
                      expiryController.text.length < 5 ||
                      cvvController.text.length < 3) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Please enter valid card details'),
                        backgroundColor: Colors.red,
                      ),
                    );
                    return;
                  }
                  
                  // Show processing state
                  setState(() {
                    isProcessing = true;
                  });
                  
                  // Simulate payment processing
                  await Future.delayed(const Duration(milliseconds: 1500));
                  
                  // Close dialog with success
                  if (context.mounted) {
                    Navigator.pop(context, true);
                  }
                },
                child: const Text('Pay Now'),
              ),
          ],
        ),
      ),
    );
    
    // Return early if payment was cancelled or failed
    if (result != true) {
      throw Exception('Payment cancelled');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Fuel'),
        automaticallyImplyLeading: false, // Remove back button
        actions: [
          // Add a custom back button if needed
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Fuel Details',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _selectedFuelType,
                  decoration: const InputDecoration(
                    labelText: 'Fuel Type',
                    border: OutlineInputBorder(),
                  ),
                  items: _fuelPrices.keys.map((String type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text('$type (PKR-${_fuelPrices[type]!}/L)'),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      setState(() {
                        _selectedFuelType = newValue;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _quantityController,
                  decoration: const InputDecoration(
                    labelText: 'Quantity (Liters)',
                    border: OutlineInputBorder(),
                    suffixText: 'L',
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter quantity';
                    }
                    final quantity = double.tryParse(value);
                    if (quantity == null || quantity <= 0) {
                      return 'Please enter a valid quantity';
                    }
                    if (quantity < 1) {
                      return 'Minimum order is 1 liters';
                    }
                    if (quantity > 50) {
                      return 'Maximum order is 50 liters';
                    }
                    return null;
                  },
                  onChanged: (value) {
                    setState(() {});
                  },
                ),
                const SizedBox(height: 24),
                const Text(
                  'Delivery Address',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _addressController,
                  decoration: InputDecoration(
                    labelText: 'Full Address',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: const Icon(Icons.location_on, color: Colors.yellow),
                      onPressed: _openMapLocationPicker,
                      tooltip: 'Select location on map',
                    ),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your address';
                    }
                    if (value.length < 10) {
                      return 'Please enter a complete address';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton.icon(
                    onPressed: _openMapLocationPicker,
                    icon: const Icon(Icons.map, color: Colors.yellow),
                    label: const Text(
                      'Select Location on Map',
                      style: TextStyle(color: Colors.yellow),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.yellow),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                const Text(
                  'Payment Method',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Column(
                  children: [
                    RadioListTile<String>(
                      title: const Text('Cash on Delivery'),
                      value: 'Cash on Delivery',
                      groupValue: _paymentMethod,
                      onChanged: (value) {
                        setState(() {
                          _paymentMethod = value!;
                        });
                      },
                    ),
                    RadioListTile<String>(
                      title: const Text('Card Payment'),
                      value: 'Card Payment',
                      groupValue: _paymentMethod,
                      onChanged: (value) {
                        setState(() {
                          _paymentMethod = value!;
                        });
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 32),
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Order Summary',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Fuel Type:'),
                            Text(_selectedFuelType),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Quantity:'),
                            Text('${_quantityController.text} L'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Price per Liter:'),
                            Text('PKR-${_fuelPrices[_selectedFuelType]}'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Subtotal:'),
                            Text('PKR-${_subtotal.toStringAsFixed(2)}'),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('Delivery Charge:'),
                            Text('PKR-${_deliveryCharge.toStringAsFixed(2)}'),
                          ],
                        ),
                        const Divider(height: 24),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Payment Method:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(_paymentMethod),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'Total Amount:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            Text(
                              'PKR-${_totalAmount.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _placeOrder,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'Place Order',
                            style: TextStyle(fontSize: 16),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}




